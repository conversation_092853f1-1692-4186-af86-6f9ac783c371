<!-- 桥隧病害处理 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="title-row">
          <span class="title">桥隧病害处理</span>
          <span class="subtitle">BRIDGE AND TUNNEL TREAT</span>
        </div>
        <div class="time-tabs">
          <span class="tab active">日</span>
          <span class="tab">月</span>
          <span class="tab">年</span>
        </div>
        <div class="legend">
          <div class="legend-item">
            <span class="legend-color untreated"></span>
            <span class="legend-text">未处理</span>
          </div>
          <div class="legend-item">
            <span class="legend-color treated"></span>
            <span class="legend-text">已处理</span>
          </div>
        </div>
      </div>
    </template>
    <template #content>
      <div class="chart-container">
        <CEcharts :option="option" />
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})

const initEcharts = () => {
  // 桥隧病害处理数据
  const bridgeData = [
    { name: '戴铁路大桥', untreated: 1, treated: 3 },
    { name: '安庄大桥', untreated: 3, treated: 1 },
    { name: '官井南隧道', untreated: 2, treated: 4 }
  ]
  const options = {
    backgroundColor: 'transparent',
    grid: {
      left: '15%',
      right: '5%',
      top: '15%',
      bottom: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: bridgeData.map(item => item.name),
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        rotate: 0,
        margin: 15
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      max: 6,
      interval: 1,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '未处理',
        type: 'bar',
        stack: 'total',
        barWidth: 20,
        itemStyle: {
          color: '#00FF7F'
        },
        data: bridgeData.map(item => item.untreated)
      },
      {
        name: '已处理',
        type: 'bar',
        stack: 'total',
        barWidth: 20,
        itemStyle: {
          color: '#4169E1'
        },
        data: bridgeData.map(item => item.treated)
      }
    ]
  }
  return options
}
onMounted(() => {
  option.value = initEcharts()
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .title-row {
    display: flex;
    align-items: baseline;
    gap: 12px;
    margin-bottom: 10px;

    .title {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
    }
  }

  .time-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;

    .tab {
      padding: 4px 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      cursor: pointer;

      &.active {
        background: rgba(0, 255, 127, 0.2);
        border-color: #00FF7F;
        color: #00FF7F;
      }
    }
  }

  .legend {
    display: flex;
    gap: 20px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .legend-color {
        width: 12px;
        height: 12px;

        &.untreated {
          background: #00FF7F;
        }

        &.treated {
          background: #4169E1;
        }
      }

      .legend-text {
        color: #fff;
        font-size: 12px;
      }
    }
  }
}

.chart-container {
  width: 100%;
  height: 200px;
}
</style>
