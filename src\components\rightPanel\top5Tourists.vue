<!-- 桥隧健康度 -->
<template>
  <CPanel>
    <template #header>桥隧健康度</template>
    <template #content>
      <CEcharts ref="chartRef" :option="option" />
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 桥隧健康度数据
const healthData = [
  { name: 'I基本完好', value: 31, color: '#00FF7F' },
  { name: 'II轻微异常', value: 28, color: '#4169E1' },
  { name: 'III中等异常', value: 18, color: '#1E90FF' },
  { name: 'IV严重异常', value: 15, color: '#00BFFF' }
]

const createHealthChart = () => {
  const total = healthData.reduce((sum, item) => sum + item.value, 0)

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'center',
      textStyle: {
        color: '#C5D6E6',
        fontSize: 14
      },
      formatter: function(name) {
        const item = healthData.find(d => d.name === name)
        return `${name}    ${item.value}`
      }
    },
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '16.3%\nIV严重异常',
          textAlign: 'center',
          fill: '#fff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ],

    series: [
      {
        name: '桥隧健康度',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderRadius: 0,
          borderColor: 'transparent',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: healthData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }
}



onMounted(() => {
  option.value = createHealthChart()
})
</script>
<style lang="scss" scoped></style>
