<template>
  <CPanel class="monitoring-alarm">
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>监测告警</span>
        <span class="header-subtitle">MONITORING AND ALARMING</span>
      </div>
    </template>
    <template #content>
      <div class="content-container">
        <div class="date-selector">
          <span class="date-icon">📅</span>
          <span class="date-text">2025-07-24 至 2025-07-30</span>
        </div>
        <CEcharts ref="chartRef" :option="option" class="monitoring-chart" />
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 监测告警数据 - 7天的数据
const monitoringData = [10, 4, 6, 2, 6, 5, 8]
const dateLabels = ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28', '2025-07-29', '2025-07-30']

const createMonitoringChart = () => {

  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: function (params) {
        const item = params[0]
        return `${item.name}<br/>告警次数: ${item.value}次`
      }
    },
    grid: {
      left: '1%',
      right: '1%',
      top: '15%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dateLabels,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(76, 93, 130, 1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 10,
        color: 'rgba(201, 211, 234, 1)',
        rotate: 0,
        formatter: function(value) {
          return value.substring(5) // 只显示月-日
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      name: '次',
      nameTextStyle: {
        color: 'rgba(201, 211, 234, 1)',
        fontSize: 12,
        padding: [0, 0, 0, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(49, 58, 86, 0.5)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      }
    },
    series: [
      {
        type: 'line',
        data: monitoringData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#00FF88',
          width: 1,
          shadowColor: 'rgba(0, 255, 136, 0.3)',
          shadowBlur: 10
        },
        itemStyle: {
          color: '#00FF88',
          borderColor: '#00FF88',
          borderWidth: 2,
          shadowColor: 'rgba(0, 255, 136, 0.5)',
          shadowBlur: 8
        },
        emphasis: {
          itemStyle: {
            color: '#00FF88',
            borderColor: '#fff',
            borderWidth: 3,
            shadowColor: 'rgba(0, 255, 136, 0.8)',
            shadowBlur: 15
          }
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 136, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 136, 0.05)'
              }
            ]
          }
        }
      }
    ]
  }
}

onMounted(() => {
  option.value = createMonitoringChart()
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #C5D6E6;
    margin-left: 8px;
  }
}

.content-container {
  padding: 16px 0;
}

.date-selector {
  display: flex;
  align-items: center;
  width: 11rem;
  gap: 8px;
  padding: 3px 7px;
  border: 1px solid #fff;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;

  .date-icon {
    font-size: 14px;
  }

  .date-text {
    color: #fff;
  }
}

.monitoring-chart {
  height: 200px;
  width: 100%;
}
</style>
